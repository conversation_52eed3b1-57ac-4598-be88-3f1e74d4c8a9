package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.graphics.Rect;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.card.MaterialCardView;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.ItemAdapter;
import com.mdsadrulhasan.gogolaundry.database.entity.ItemEntity;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.util.GridSpacingItemDecoration;
import com.mdsadrulhasan.gogolaundry.viewmodel.CartViewModel;
import com.mdsadrulhasan.gogolaundry.viewmodel.ItemsViewModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Fragment for displaying items for a service
 */
public class ItemsFragment extends Fragment implements ItemAdapter.ItemClickListener {

    private static final String ARG_SERVICE_ID = "service_id";
    private static final String ARG_SERVICE_NAME = "service_name";

    private ItemsViewModel viewModel;
    private CartViewModel cartViewModel;
    private RecyclerView recyclerView;
    private ItemAdapter adapter;
    private ProgressBar progressBar;
    private TextView emptyView;
    private TextView serviceTitle;
    private FrameLayout progressCard;
    private MaterialCardView emptyCard;

    private SwipeRefreshLayout swipeRefreshLayout;
    private int serviceId;
    private String serviceName;

    /**
     * Create a new instance of ItemsFragment
     *
     * @param serviceId Service ID
     * @param serviceName Service name
     * @return ItemsFragment instance
     */
    public static ItemsFragment newInstance(int serviceId, String serviceName) {
        ItemsFragment fragment = new ItemsFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_SERVICE_ID, serviceId);
        args.putString(ARG_SERVICE_NAME, serviceName);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Get arguments
        if (getArguments() != null) {
            serviceId = getArguments().getInt(ARG_SERVICE_ID);
            serviceName = getArguments().getString(ARG_SERVICE_NAME);
        }

        // Initialize ViewModels
        viewModel = new ViewModelProvider(this).get(ItemsViewModel.class);
        cartViewModel = new ViewModelProvider(requireActivity()).get(CartViewModel.class);

        // Set service ID filter
        if (serviceId > 0) {
            viewModel.setServiceId(serviceId);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_items, container, false);

        // Initialize views
        recyclerView = view.findViewById(R.id.items_recycler_view);
        progressBar = view.findViewById(R.id.progress_bar);
        progressCard = view.findViewById(R.id.progress_card);
        emptyView = view.findViewById(R.id.empty_view);
        emptyCard = view.findViewById(R.id.empty_card);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);

        // Initialize header views
        serviceTitle = view.findViewById(R.id.service_title);

        // Set service title
        if (serviceName != null) {
            serviceTitle.setText(serviceName);
        }

        // Set up Enhanced Responsive RecyclerView
        setupRecyclerView();
        adapter = new ItemAdapter(new ArrayList<>(), this);
        recyclerView.setAdapter(adapter);

        // Set up SwipeRefreshLayout
        swipeRefreshLayout.setOnRefreshListener(() -> {
            viewModel.refreshItems();
        });

        return view;
    }

    /**
     * Set up RecyclerView with GridLayoutManager for 2-column layout
     */
    private void setupRecyclerView() {
        if (getContext() == null) return;

        // Create GridLayoutManager for 2-column layout to match the item layout design
        GridLayoutManager layoutManager = new GridLayoutManager(getContext(), 2);

        // Enable layout optimization for better performance
        layoutManager.setRecycleChildrenOnDetach(true);

        // Set the layout manager
        recyclerView.setLayoutManager(layoutManager);

        // Enable performance optimizations
        recyclerView.setHasFixedSize(true);
        recyclerView.setItemViewCacheSize(20);
        recyclerView.setDrawingCacheEnabled(true);
        recyclerView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);

        // Add item decoration for better spacing
        int spacing = getResources().getDimensionPixelSize(R.dimen.margin_small);
        recyclerView.addItemDecoration(new GridSpacingItemDecoration(2, spacing, true));

        // Add scroll listener for better performance
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                // Optimize performance during scrolling
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    // Re-enable animations when scrolling stops
                    recyclerView.setLayoutAnimation(null);
                }
            }
        });
    }







    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Set title if service name is available
        if (serviceName != null && !serviceName.isEmpty() && getActivity() != null) {
            getActivity().setTitle(serviceName);
        }

        // Observe items data
        viewModel.getItems().observe(getViewLifecycleOwner(), resource -> {
            swipeRefreshLayout.setRefreshing(false);

            if (resource == null) {
                showError("No data received");
                return;
            }

            if (resource.isLoading()) {
                showLoading();
            } else if (resource.isSuccess()) {
                List<ItemEntity> itemEntities = resource.getData();
                if (itemEntities != null && !itemEntities.isEmpty()) {
                    // Convert entities to UI models
                    List<Item> items = viewModel.convertToItemModels(itemEntities);
                    showItems(items);
                } else {
                    showEmpty();
                }
            } else if (resource.isError()) {
                String errorMessage = resource.getMessage();
                showError(errorMessage != null ? errorMessage : "Unknown error occurred");
            }
        });

        // Trigger initial data loading if serviceId is valid
        if (serviceId > 0) {
            viewModel.refreshItems();
        } else {
            showError("Invalid service ID");
        }
    }

    /**
     * Show loading state
     */
    private void showLoading() {
        progressCard.setVisibility(View.VISIBLE);
        recyclerView.setVisibility(View.GONE);
        emptyCard.setVisibility(View.GONE);
    }

    /**
     * Show items data
     *
     * @param items List of items to display
     */
    private void showItems(List<Item> items) {
        progressCard.setVisibility(View.GONE);
        recyclerView.setVisibility(View.VISIBLE);
        emptyCard.setVisibility(View.GONE);
        adapter.updateItems(items);
    }

    /**
     * Show empty state
     */
    private void showEmpty() {
        progressCard.setVisibility(View.GONE);
        recyclerView.setVisibility(View.GONE);
        emptyCard.setVisibility(View.VISIBLE);

        // Show service-specific empty message
        if (serviceName != null && !serviceName.isEmpty()) {
            emptyView.setText("No items available for " + serviceName + " service.\nPlease check back later or contact support.");
        } else {
            emptyView.setText(getString(R.string.no_items_found));
        }
    }

    /**
     * Show error state
     *
     * @param message Error message
     */
    private void showError(String message) {
        progressCard.setVisibility(View.GONE);
        Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();

        // If adapter is empty, show empty view with error message
        if (adapter.getItemCount() == 0) {
            recyclerView.setVisibility(View.GONE);
            emptyCard.setVisibility(View.VISIBLE);
            emptyView.setText(getString(R.string.error) + ": " + message);
        }
    }



    @Override
    public void onItemClicked(Item item) {
        // Show item details
        Toast.makeText(getContext(), "Item clicked: " + item.getName(), Toast.LENGTH_SHORT).show();

        // TODO: Implement navigation to item details
    }

    /**
     * Get the service name for this fragment
     *
     * @return Service name
     */
    public String getServiceName() {
        return serviceName != null ? serviceName : "";
    }

    @Override
    public void onAddToCartClicked(Item item, int position) {
        // Add item to cart
        if (cartViewModel.addToCart(item, 1)) {
            Toast.makeText(getContext(), item.getName() + " added to cart", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(getContext(), "Failed to add item to cart", Toast.LENGTH_SHORT).show();
        }
    }


}
