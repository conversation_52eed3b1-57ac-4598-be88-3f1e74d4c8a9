[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_cart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_cart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_clean_content_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\clean_content_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_popular_services.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_popular_services.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_empty_state_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_empty_state_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_kalpurush.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\kalpurush.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_shop_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_shop_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_li_ador_noirrit_regular.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\li_ador_noirrit_regular.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_location.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_location.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_invoice_row.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_invoice_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_gradient_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\gradient_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_mt.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\mt.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_rounded_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\rounded_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-pngs-49:\\drawable-xxxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_home_hero_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\home_hero_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_dry_cleaning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_dry_cleaning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_cart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_cart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_baseline_password_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\baseline_password_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_shop_service_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_shop_service_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_filter_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_filter_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_icon_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_icon_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_address_picker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_address_picker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_gradient_overlay_bottom.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\gradient_overlay_bottom.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_baseline_phone_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\baseline_phone_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bottom_sheet_handle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bottom_sheet_handle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_badge_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\badge_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_search_bar_focused.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_search_bar_focused.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_content_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_content_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_recent_order_shimmer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_recent_order_shimmer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_map.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_map.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_fullscreen_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_fullscreen_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_dialog_image_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\dialog_image_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_iron.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_iron.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\color_nav_item_background_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\color\\nav_item_background_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_placeholder_image.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\placeholder_image.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-pngs-49:\\drawable-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_english.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\english.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_refresh.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_refresh.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_star_filled.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_star_filled.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_my_location.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_my_location.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_hero_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_hero_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_rating_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\rating_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_nav_item_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_nav_item_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-pngs-49:\\drawable-xxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_shop_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_shop_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_shimmer_circle.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\shimmer_circle.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_laundry_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_laundry_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_share.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_share.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_duowg.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\duowg.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_service_chip.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_service_chip.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_washing_machine.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_washing_machine.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_popular_service.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_popular_service.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_layout_notification_badge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\layout_notification_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_material_icon_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\material_icon_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_drawer_shadow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\drawer_shadow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_shimmer_rounded.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\shimmer_rounded.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_cart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_cart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_custom_toast.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\custom_toast.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_status_history.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_status_history.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_clean_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\clean_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout-sw320dp_nav_header_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout-sw320dp\\nav_header_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_recent_order.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_recent_order.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_edit.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_edit.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_status_badge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_status_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_mahfuj.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\mahfuj.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_search_bar_ripple_effect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\search_bar_ripple_effect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_notifications.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\color_nav_item_color_state.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\color\\nav_item_color_state.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_stain_removal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_stain_removal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_order_tracking.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_order_tracking.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\anim_fade_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\anim\\fade_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_layout_cart_badge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\layout_cart_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_shop_services.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_shop_services.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_compact_service_image_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\compact_service_image_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_badge_open.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\badge_open.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_promo_close_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\promo_close_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_calendar_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_calendar_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_time.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_time.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_spinner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\spinner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_toast_warning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_toast_warning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_laundry_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_laundry_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\anim_slide_out_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\anim\\slide_out_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_digit.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\digit.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_activity_main_drawer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\activity_main_drawer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_enhanced_service_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\enhanced_service_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\anim_dialog_scale_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\anim\\dialog_scale_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_admin_number_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\admin_number_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_btxf.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\btxf.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\anim_slide_out_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\anim\\slide_out_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-pngs-49:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_button_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\button_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_fgg.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\fgg.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_home_modern_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\home_modern_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_status_indicator_active.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\status_indicator_active.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_service_shimmer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_service_shimmer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_gradient_blue_purple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\gradient_blue_purple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_shop_item_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_shop_item_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_review.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_review.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_banglanm.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\banglanm.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_activity_forgot_password.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\activity_forgot_password.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_mahfuj2.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\mahfuj2.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\animator_card_press_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\animator\\card_press_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_button_primary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\button_primary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_add_shopping_cart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_add_shopping_cart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_bnlatxf.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\bnlatxf.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_item_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_item_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_home_ripple_effect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\home_ripple_effect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\animator_date_time_card_state_animator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\animator\\date_time_card_state_animator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_service_accent_line.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\service_accent_line.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_clean_section_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\clean_section_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\menu_drawer_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\menu\\drawer_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_shop_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_shop_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_services.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_services.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_logout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_logout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_toolbar_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_toolbar_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_button_secondary.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\button_secondary.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_service_horizontal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_service_horizontal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_baseline_sms_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\baseline_sms_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\anim_dialog_scale_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\anim\\dialog_scale_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_price_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\price_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_popular_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_popular_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_price_tag_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\price_tag_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_search_bar_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_search_bar_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_background_pattern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\background_pattern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_dialog_shop_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\dialog_shop_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_shop_service.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_shop_service.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_laundry_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_laundry_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-pngs-49:\\drawable-xhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_approved.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\approved.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\animator_card_release_animation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\animator\\card_release_animation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_service_grid.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_service_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_phone.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_phone.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_popular_item_shimmer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_popular_item_shimmer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_bottom_nav_cart_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\bottom_nav_cart_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_checkout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_checkout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_shop_marker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_shop_marker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_search_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_search_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_nav_header_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\nav_header_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_srg.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\srg.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_cancelled_status.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_cancelled_status.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_filter_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_filter_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_search_bar_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_search_bar_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_ots.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\ots.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\color_chip_text_color_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\color\\chip_text_color_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_email.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_email.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_gradient_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_gradient_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_service.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_service.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bottom_nav_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bottom_nav_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_shop_map.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_shop_map.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_input_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_input_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_empty_box.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_empty_box.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_order_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_order_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_shopping_cart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_shopping_cart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_toast_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_toast_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_ott.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\ott.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_payment.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_payment.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_navigation_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_navigation_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_reorder.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\reorder.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_service_card_shine_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\service_card_shine_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_shimmer_shop_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\shimmer_shop_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_toast_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_toast_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_layout_recent_orders_shimmer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\layout_recent_orders_shimmer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_promo_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\promo_dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_delivery.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_delivery.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_time_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_time_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_search_suggestions_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\search_suggestions_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_layout_popular_items_shimmer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\layout_popular_items_shimmer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable-mdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-pngs-49:\\drawable-mdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_button_accent.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\button_accent.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_info.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_info.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_payment_details_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\payment_details_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\color_chip_background_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\color\\chip_background_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_help.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_help.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_folding.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_folding.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\color_bottom_nav_item_color.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\color\\bottom_nav_item_color.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_print.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_print.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_circle_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\circle_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_button_success.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\button_success.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_shimmer_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\shimmer_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_gradient_dark_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\gradient_dark_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_home_gradient_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\home_gradient_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_home_floating_button.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\home_floating_button.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_hind.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\hind.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_service.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_service.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_badge_closed.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\badge_closed.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bottom_nav_indicator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bottom_nav_indicator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_service_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_service_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_material_card_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\material_card_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_search_bar_gradient_overlay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\search_bar_gradient_overlay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_warning.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_warning.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_shop_cover_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\shop_cover_placeholder.xml"}, {"merged": "com.mdsadrulhasan.gogolaundry.app-debug-54:/layout_fragment_items.xml.flat", "source": "com.mdsadrulhasan.gogolaundry.app-main-56:/layout/fragment_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_shop_details_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_shop_details_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_gradient_overlay_top.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\gradient_overlay_top.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_operating_hours.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_operating_hours.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_date_time_picker_ripple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_date_time_picker_ripple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_star_half.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_star_half.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_order_grid.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_order_grid.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_service_icon_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\service_icon_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_popular_laundry.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_popular_laundry.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_empty_state_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_empty_state_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_date_time_picker_selected.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_date_time_picker_selected.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_shop_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_shop_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout-land_nav_header_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout-land\\nav_header_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_success.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_success.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_gradient_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\gradient_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_profile.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\profile.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_empty_state.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_empty_state.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_directions.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_directions.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_arrow_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_arrow_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_rounded_corner_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\rounded_corner_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_scrollbar_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\scrollbar_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_laundry_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_laundry_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_layout_services_shimmer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\layout_services_shimmer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_tab_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\tab_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_activity_login_no_otp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\activity_login_no_otp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_checkout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_checkout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_fdg.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\fdg.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_dialog_promo_sale.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\dialog_promo_sale.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_arrow_forward.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_arrow_forward.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_bottom_nav_glass.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_bottom_nav_glass.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_services.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_services.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_mtg.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\mtg.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_orders.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_orders.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_modern_status_badge_open.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\modern_status_badge_open.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_date_time_picker_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_date_time_picker_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_clock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_clock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_notification_details.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_notification_details.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\animator_card_state_list_animator.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\animator\\card_state_list_animator.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_clean_bottom_nav_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\clean_bottom_nav_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_shimmer_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\shimmer_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_nav_header_enhanced.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\nav_header_enhanced.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_shop_placeholder.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_shop_placeholder.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_receipt.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_receipt.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_status_indicator_inactive.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\status_indicator_inactive.xml"}, {"merged": "com.mdsadrulhasan.gogolaundry.app-debug-54:/layout_fragment_services.xml.flat", "source": "com.mdsadrulhasan.gogolaundry.app-main-56:/layout/fragment_services.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_dialog_select_items.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\dialog_select_items.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_custom_toolbar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\custom_toolbar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_subtle_pattern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\subtle_pattern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_search_modern.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_search_modern.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_lock.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_lock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_error.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_error.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\anim_slide_in_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\anim\\slide_in_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_profile.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_profile.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_custom_bottom_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\custom_bottom_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_out_of_stock_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\out_of_stock_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_akar_font.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\akar_font.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_popular_item_horizontal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_popular_item_horizontal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_empty_services.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_empty_services.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_custom_navigation_drawer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\custom_navigation_drawer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable-ldpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-pngs-49:\\drawable-ldpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_section_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_section_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_activity_signup.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\activity_signup.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_profile_image_ripple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\profile_image_ripple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_home_glass_card.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\home_glass_card.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_glass_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\glass_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_home_section_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\home_section_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_material_button_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\material_button_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bg_toast_success.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bg_toast_success.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_star_empty.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_star_empty.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_bottom_nav_ripple.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\bottom_nav_ripple.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_item_order.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\item_order.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\layout_fragment_invoice.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\layout\\fragment_invoice.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_arrow_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_arrow_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_person.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_person.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_elcias.otf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\elcias.otf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_header_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\header_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_ic_remove.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\ic_remove.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\font_bnlatx.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\font\\bnlatx.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-debug-54:\\drawable_nav_item_selected_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-main-56:\\drawable\\nav_item_selected_bg.xml"}]